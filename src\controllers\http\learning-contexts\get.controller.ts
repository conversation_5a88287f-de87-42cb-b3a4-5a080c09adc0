import get from '../../../services/mssql/learning-context/get.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import learningContextExtrasMapper from '../../../mappers/learning-context-extras.mapper.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID, zodStringToBoolean } from '@tess-f/backend-utils/validators'
const { INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'
import isInstructor from '../../../services/mssql/learning-context/get-instructors.service.js'
import isUserEnrolled from '../../../services/mssql/learning-context/get-enrolled.service.js'

const log = logger.create('Controller-HTTP.get-learning-context', httpLogTransformer)

/*
    Query options
        prerequisites: Boolean
        nestedContexts: Boolean
        incViews: Boolean
        rating: Boolean

*/
export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-learning-context' label."

  try {
    // Parse the options
    const options = z.object({
      prerequisites: zodStringToBoolean.optional().default('false'),
      nestedContexts: zodStringToBoolean.optional().default('false'),
      incViews: zodStringToBoolean.optional().default('false'),
      rating: zodStringToBoolean.optional().default('false'),
      upcomingSessionCount: zodStringToBoolean.optional().default('false')
    }).parse(req.query)
    const { id } = z.object({ id: zodGUID }).parse(req.params)

    // Result contains the context as well as the optional data
    // get the learning context with the given ID and the options specified
    // the options are parsed from the query string of the HTTP request
    // the resources option, if specified, indicates whether to load the resources of the context
    // the result contains the learning context as well as the resources (if specified)
    const result = await get(id, req.session.userId, { ...options, resources: true })
    await learningContextExtrasMapper([result], req.session.userId, {
      favorite: true,
      bookmark: true,
      rating: true,
      completion: true
    })
    const context = result.fields

    // get user bookmarks / favorites / ratings / duration / completion for each nested context
    if (context.Contexts) {
      context.Contexts = await mapExtras(context.Contexts, req.session.userId)
    }
    
    const isUserInstructor = await isInstructor(context.ID ?? '', req.session.userId)
    const isEnrolled = await isUserEnrolled(context.ID ?? '', req.session.userId)

    // the user can only view the resources if they are:
    // enrolled in the course and are viewing it
    // instructing the course and are viewing it
    // can create/modify course and are not on the view page
    // users are considered to be viewing the context if incViews is true
    if (
      context.Resources
      && options.incViews
      && isEnrolled
      && !isUserInstructor
    ) {
      context.Resources = context.Resources.filter(resource => resource.StudentAccessible)
      log('verbose', 'Mapped only the student accessible resources', { id: context.ID, success: true, req })
    } else if (
      (!options.incViews && !(req.claims.includes(Claims.CREATE_COURSE) || req.claims.includes(Claims.MODIFY_COURSE))) ||
      (options.incViews && !isEnrolled && !isUserInstructor)
    ) {
      context.Resources = []
      log('verbose', 'User is not viewing as instructor or enrollee or not viewing as editor, removed resources', { id: context.ID, success: true, req, isViewing: options.incViews, isUserEnrolled: isEnrolled, isUserInstructor })
    }

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-learning-context' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved learning context', { id: context.ID, success: true, req })

    res.json(context)
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning context because it was not found in the database.', { id: req.params.id, success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to get learning context.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}

async function mapExtras (contexts: LearningContext[], userID: string): Promise<LearningContext[]> {
  const nested = contexts.map(nest => new LearningContextModel(nest))
  await learningContextExtrasMapper(nested, userID, {
    rating: true,
    bookmark: true,
    favorite: true,
    duration: true,
    completion: true
  })
  for (const child of nested) {
    if (child.fields.Contexts && child.fields.Contexts.length > 0) {
      child.fields.Contexts = await mapExtras(child.fields.Contexts, userID)
    }
  }
  return nested.map(nest => nest.fields)
}
