import { Table } from '@lcs/mssql-utility'
import {  ExternalReview, ExternalReviewFields, ExternalReviewTableName } from '@tess-f/sql-tables/dist/lms/external-review.js'
import z from 'zod'
import {ExternalReviewReviewStatuses} from '@tess-f/sql-tables/dist/lms/external-review-status.js'




export const additionalFields = z.object({
  [ExternalReviewFields.StatusId]: z.nativeEnum(ExternalReviewReviewStatuses).nullable().optional(), // ExternalReviewFields
  [ExternalReviewFields.LearnerProgressId] : z.string().uuid(),
  [ExternalReviewFields.CreatedOn] : z.date()
})

//export const createMessageReviewSchema = createMessageSchema.merge(additionalFields)
export default class ExternalReviewModel extends Table<ExternalReview, ExternalReview> {
  public fields: ExternalReview

  constructor (fields?: ExternalReview) {
    super(ExternalReviewTableName, [
      
      ExternalReviewFields.ModifiedBy,
      ExternalReviewFields.CreatedOn,
      ExternalReviewFields.LearnerProgressId,
      ExternalReviewFields.StatusId
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: ExternalReview): void {
    this.fields = record
  }

  public exportJsonToDatabase (): ExternalReview {
    return this.fields
  }
}
