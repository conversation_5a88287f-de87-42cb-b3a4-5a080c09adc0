import mssql, { getRows } from '@lcs/mssql-utility'
import ExternalReviewModel from '../../../models/external-review.model.js'
import { ExternalReview, ExternalReviewFields , ExternalReviewTableName } from '@tess-f/sql-tables/dist/lms/external-review.js'

export default async function (reviewId: string): Promise<ExternalReviewModel> {
  const pool = mssql.getPool()
  const record = await getRows<ExternalReview>(ExternalReviewTableName, pool.request(), { Id : reviewId })
  return new ExternalReviewModel(record[0])
}
