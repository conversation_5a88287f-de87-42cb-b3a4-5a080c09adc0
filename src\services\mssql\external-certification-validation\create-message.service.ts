import mssql, { addRow } from '@lcs/mssql-utility'
import { ExternalReviewCommunication } from '@tess-f/sql-tables/dist/lms/external-review-communication.js'
import ExternalReviewModel from '../../../models/external-review-communications.model.js'

export default async function (message: ExternalReviewModel): Promise<ExternalReviewModel> {
  const pool = mssql.getPool()
  const record = await addRow<ExternalReviewCommunication>(pool.request(), message)
  return new ExternalReviewModel(record)
}
