{"name": "tess-lms", "version": "2.1.4", "private": true, "license": "Northrop Grumman", "main": "src/index.ts", "exports": "./build/index.js", "type": "module", "repository": {"type": "git", "url": "https://github.northgrum.com/LCS-TESS/TESS-LMS-Server.git"}, "scripts": {"start": "node build/index.js", "start:dev": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./tess-config.yaml nodemon --inspect", "start:csmu-proxy": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./csmu-config.yaml nodemon --inspect", "build": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./tess-config.yaml rimraf ./build && tsc && npm run copy-files", "copy-files": "copyfiles -u 1 src/**/*.txt src/**/*.env src/**/*.json src/**/*.png src/**/*.jpg src/**/*.ttf build/", "test": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./test-config.yaml NODE_OPTIONS='--loader=ts-node/esm' mocha --exit --timeout 999999", "test:coverage": "c8 --reporter=html --reporter=text --reporter=text-summary --reporter=lcov --exclude ./src/**/*.spec.ts --include ./src/**/*.spec.ts --all npm test", "test:controllers": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./test-config.yaml NODE_OPTIONS='--loader=ts-node/esm' mocha src/controllers/**/*.spec.ts --exit --timeout 999999", "test:controllers:coverage": "c8 --reporter=html --reporter=text --reporter=text-summary --exclude ./src/**/*.spec.ts --include ./src/**/*.spec.ts --all npm run test:controllers", "test:services": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./test-config.yaml NODE_OPTIONS='--loader=ts-node/esm' mocha src/services/**/*.spec.ts --exit", "test:services:coverage": "c8 --reporter=html --reporter=text --reporter=text-summary --exclude ./src/**/*.spec.ts --include ./src/**/*.spec.ts --all npm run test:services"}, "description": "TESS LMS", "author": {"name": "Northrop Grumman"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/chai": "^5.2.2", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/mssql": "^9.1.7", "@types/node": "^22.14.1", "@types/pdfkit": "^0.14.0", "@types/prettyjson": "^0.0.33", "@types/sinon": "^17.0.4", "@types/uuid": "^10.0.0", "c8": "^10.1.3", "chai": "^5.2.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^9.29.0", "esmock": "^2.7.0", "globals": "^16.2.0", "mocha": "^11.7.0", "mocha-junit-reporter": "^2.2.1", "mocha-multi": "^1.1.7", "node-mocks-http": "^1.17.2", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "sinon": "^21.0.0", "ts-mocha": "^11.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "uuid": "^11.1.0"}, "dependencies": {"@json2csv/plainjs": "^7.0.6", "@lcs/logger": "^4.0.3", "@lcs/mssql-utility": "^3.0.0", "@lcs/rabbitmq": "^4.0.2", "@lcs/session-authority": "^4.0.0", "@tess-f/backend-utils": "^2.0.2", "@tess-f/email": "^3.0.2", "@tess-f/fds": "^2.0.0", "@tess-f/lms": "^2.0.7", "@tess-f/objectives": "^2.0.3", "@tess-f/shared-config": "^2.0.10", "@tess-f/sql-tables": "^2.3.4", "@tess-f/system-config": "^2.0.3", "body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "excel4node": "^1.8.2", "express": "^5.1.0", "http-status": "^2.1.0", "minimist": "^1.2.8", "moment-timezone": "^0.6.0", "mssql": "^11.0.1", "pdfkit": "^0.17.1", "prettyjson": "^1.2.5", "prom-client": "^15.1.3", "redis": "^5.5.6", "zod": "^3.25.67"}, "overrides": {"es5-ext": "^0.10.53", "@redis/client": "^1.5.8", "@redis/time-series": "^1.0.4", "ws": "^8.17.1", "async": "^3.2.6", "cookie": "^1.0.1", "image-size": "^1.2.1"}, "mocha": {"reporter": "mocha-multi", "reporterOptions": "spec=-,mocha-junit-reporter=-"}}