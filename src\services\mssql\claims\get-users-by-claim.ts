import logger from '@lcs/logger'
import mssql from '@lcs/mssql-utility'
import { GroupClaimsTableName, GroupClaimFields } from '@tess-f/sql-tables/dist/lms/group-claim.js'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service-MSSQL.get-users-by-claim')

export default async function (claim: string): Promise<User[]> {
  try {
    const pool = mssql.getPool()
    const request = pool.request()
    request.input('claim', claim)

    const results = await request.query<User>(`
    SELECT DISTINCT u.*
    FROM [${UserTableName}] u
    INNER JOIN [${UserGroupTableName}] ug ON u.[${UserFields.ID}] = ug.[${UserGroupFields.UserID}]
    INNER JOIN [${GroupClaimsTableName}] gc ON ug.[${UserGroupFields.GroupID}] = gc.[${GroupClaimFields.GroupID}]
    WHERE gc.[${GroupClaimFields.Claim}] = @claim
  `)

    return results.recordset
  } catch (error) {
    // Unexpected error
    log('error', 'Database error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
