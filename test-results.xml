<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="6.068" tests="4" failures="2">
  <testsuite name="Root Suite" timestamp="2025-08-14T18:13:33" tests="0" time="0.000" failures="0">
  </testsuite>
  <testsuite name="HTTP get controller" timestamp="2025-08-14T18:13:33" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\TESS-LMS-Server\src\controllers\http\learning-contexts\get.controller.spec.ts" time="6.062" failures="2">
    <testcase name="HTTP get controller returns success if the request data is valid" time="5.558" classname="returns success if the request data is valid">
      <failure message="expected 500 to equal 200" type="AssertionError"><![CDATA[AssertionError: expected 500 to equal 200
    at Context.<anonymous> (file:///C:/Users/<USER>/.local/Dev/TMS/TESS-LMS-Server/src/controllers/http/learning-contexts/get.controller.spec.ts:37:38)

      + expected - actual

      -500
      +200
      ]]></failure>
    </testcase>
    <testcase name="HTTP get controller returns success if the request data is valid" time="0.120" classname="returns success if the request data is valid">
      <failure message="expected 500 to equal 200" type="AssertionError"><![CDATA[AssertionError: expected 500 to equal 200
    at Context.<anonymous> (file:///C:/Users/<USER>/.local/Dev/TMS/TESS-LMS-Server/src/controllers/http/learning-contexts/get.controller.spec.ts:66:38)

      + expected - actual

      -500
      +200
      ]]></failure>
    </testcase>
    <testcase name="HTTP get controller returns an error if the request data is invalid" time="0.126" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP get controller returns an internal server error if the request is rejected" time="0.095" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
</testsuites>