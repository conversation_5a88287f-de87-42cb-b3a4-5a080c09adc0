<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="1.161" tests="2" failures="0">
  <testsuite name="Root Suite" timestamp="2025-07-15T14:46:29" tests="0" time="0.000" failures="0">
  </testsuite>
  <testsuite name="HTTP Create controller" timestamp="2025-07-15T14:46:29" tests="2" file="C:\Users\<USER>\.local\Dev\TMS\TESS-LMS-Server\src\controllers\http\external-certification-validation\create.controller.spec.ts" time="1.151" failures="0">
    <testcase name="HTTP Create controller returns success if the request data is valid" time="0.973" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP Create controller returns an error if the request data is invalid, missing status id" time="0.087" classname="returns an error if the request data is invalid, missing status id">
    </testcase>
  </testsuite>
</testsuites>